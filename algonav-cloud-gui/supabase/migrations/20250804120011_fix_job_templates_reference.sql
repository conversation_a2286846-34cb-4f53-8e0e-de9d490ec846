-- Migration: Clarify job_templates vs global_job_templates usage
-- Description: Document and verify correct usage of job template tables

BEGIN;

-- Document the purpose of both tables
DO $$
DECLARE
    job_templates_count INTEGER;
    global_job_templates_count INTEGER;
BEGIN
    -- Check both tables
    SELECT COUNT(*) INTO job_templates_count FROM job_templates;
    SELECT COUNT(*) INTO global_job_templates_count FROM global_job_templates;

    RAISE NOTICE 'Table analysis:';
    RAISE NOTICE '- job_templates: % records (user-specific templates with timestamps)', job_templates_count;
    RAISE NOTICE '- global_job_templates: % records (system templates with template_data)', global_job_templates_count;

    -- Both tables serve different purposes and should coexist
    RAISE NOTICE 'Both tables are valid and serve different purposes:';
    RAISE NOTICE '- global_job_templates: System-wide job templates with complex template_data';
    RAISE NOTICE '- job_templates: User-specific job templates with simpler structure';
END$$;

-- Verify that all job template references use global_job_templates
DO $$
BEGIN
    -- Check job_template_files references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'job_template_files'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: job_template_files correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: job_template_files does not reference global_job_templates';
    END IF;
    
    -- Check category_job_templates references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'category_job_templates'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: category_job_templates correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: category_job_templates does not reference global_job_templates';
    END IF;
    
    -- Check tasks table references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'tasks'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: tasks table correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: tasks table does not reference global_job_templates';
    END IF;
END$$;

-- Verify GIN indexes on global_job_templates exist
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename = 'global_job_templates' 
        AND indexname = 'idx_global_job_templates_template_data_gin'
    ) THEN
        RAISE NOTICE 'VERIFIED: GIN index on global_job_templates.template_data exists';
    ELSE
        RAISE WARNING 'ISSUE: GIN index on global_job_templates.template_data missing';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename = 'global_job_templates' 
        AND indexname = 'idx_global_job_templates_vars_gin'
    ) THEN
        RAISE NOTICE 'VERIFIED: GIN index on global_job_templates.vars exists';
    ELSE
        RAISE WARNING 'ISSUE: GIN index on global_job_templates.vars missing';
    END IF;
END$$;

-- Add comments for documentation
COMMENT ON TABLE global_job_templates IS 'Global job templates used throughout the system - this is the primary job template table';

COMMIT;
